<!-- TreeItem.vue -->
<template>
  <view>
    <view @click="toggleNode">{{ item.name }}</view>
    <view v-if="item.children && item.children.length && expanded">
      <tree-item v-for="child in item.children" :key="child.id" :item="child"></tree-item>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'TreeItem',
    props: ['item'],
    data() {
      return {
        expanded: false // 初始状态为折叠
      };
    },
    methods: {
      toggleNode() {
        this.expanded = !this.expanded; // 切换展开和折叠状态
      }
    }
  }
</script>
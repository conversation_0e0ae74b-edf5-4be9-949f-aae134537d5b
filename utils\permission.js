import store from '@/store'

/**
 * 字符权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkPermi(value) {
  console.log('checkPermi 输入值:', value);
  
  // 验证输入参数
  if (!value || !Array.isArray(value) || value.length === 0) {
    console.error(`权限检查需要数组格式参数，例如: ['system:user:add']`);
    return false;
  }

  // 获取权限列表
  const permissions = store.getters.permissions || [];
  const roles = store.getters.roles || [];
  console.log('当前用户权限列表:', permissions);
  console.log('当前用户角色列表:', roles);

  if (!Array.isArray(permissions)) {
    console.error('权限数据格式错误');
    return false;
  }

  // 如果有超级管理员权限或角色，直接返回 true
  if (permissions.includes('*:*:*') || roles.includes('admin')) {
    console.log('用户具有超级管理员权限或角色');
    return true;
  }

  // 检查具体权限
  const hasPermission = value.some(v => permissions.includes(v));
  console.log('具体权限检查结果:', hasPermission);
  return hasPermission;
}

/**
 * 角色权限校验
 * @param {Array} value 校验值
 * @returns {Boolean}
 */
export function checkRole(value) {
  console.log('checkRole 输入值:', value);
  
  // 验证输入参数
  if (!value || !Array.isArray(value) || value.length === 0) {
    console.error(`角色检查需要数组格式参数，例如: ['admin']`);
    return false;
  }

  // 获取角色列表和权限列表
  const roles = store.getters.roles || [];
  const permissions = store.getters.permissions || [];
  console.log('当前用户角色列表:', roles);
  console.log('当前用户权限列表:', permissions);
  
  if (!Array.isArray(roles)) {
    console.error('角色数据格式错误');
    return false;
  }

  // 如果有超级管理员权限，直接返回 true
  if (permissions.includes('*:*:*')) {
    console.log('用户具有超级管理员权限');
    return true;
  }

  // 检查具体角色
  const hasRole = value.some(v => roles.includes(v));
  console.log('角色检查结果:', hasRole);
  return hasRole;
}

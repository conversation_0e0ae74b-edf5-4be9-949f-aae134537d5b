<template>
  <view class="device-container">
    <!-- 搜索区域 -->
    <uni-section title="搜索条件" type="line">
      <view class="search-box">
        <view class="search-item">
          <input v-model="queryParams.deviceName" placeholder="请输入设备名称" class="search-input" />
        </view>
        <view class="search-item">
          <input v-model="queryParams.deviceCode" placeholder="请输入设备编码" class="search-input" />
        </view>
        <button @click="handleQuery" class="cu-btn bg-blue">搜索</button>
        <button @click="resetQuery" class="cu-btn line-blue">重置</button>
        <button @click="refreshDeviceList" class="cu-btn bg-green">刷新</button>
      </view>
    </uni-section>
    
    <!-- 设备列表 -->
    <uni-section title="设备列表" type="line">
      <view class="list-box">
        <view v-if="deviceList.length === 0" class="empty-box">
          暂无数据
        </view>
        <template v-else>
          <view v-for="device in deviceList" :key="device.id" class="list-item">
            <view class="item-main">
              <view class="item-title">
                <text class="device-name">{{device.deviceName}}</text>
                <text class="device-status" :class="{'status-online': device.connectionStatus === 'CONNECTED', 'status-offline': device.connectionStatus !== 'CONNECTED'}">{{getStatusText(device.connectionStatus)}}</text>
              </view>
              <view class="item-info">
                <!-- <text>设备编码：{{device.deviceCode}}</text> -->
                <!-- <text>设备类型：{{device.deviceType}}</text> -->
                <!-- <text>设备名称：{{device.deviceName}}</text> -->
                <text>设备IP：{{device.deviceIp}}</text>
                <text>最后心跳：{{formatDate(device.lastHeartbeatTime)}}</text>
              </view>
            </view>
            <view class="item-actions">
              <button type="primary" size="mini" @click="handleOpenGate(device.id)" :disabled="device.connectionStatus !== 'CONNECTED'" class="action-btn">开闸</button>
              <button type="default" size="mini" @click="viewDeviceDetail(device.id)" class="action-btn">详情</button>
            </view>
          </view>
        </template>
      </view>
    </uni-section>
    
    <!-- 加载更多组件 -->
    <uni-load-more :status="loadMoreStatus"></uni-load-more>
  </view>
</template>

<script>
import { getDeviceStatus, openGate, getDevice } from '@/api/system/device';
import { checkPermi } from '@/utils/permission';

export default {
  data() {
    return {
      deviceList: [],
      loadMoreStatus: 'more',
      totalPage: 0,
      currentPage: 1,
      pageSize: 10,
      queryParams: {
        deviceName: '',
        deviceCode: '',
        pageNum: 1,
        pageSize: 10
      }
    }
  },
  onLoad() {
    this.getDeviceList();
  },
  methods: {
    // 查询列表
    getDeviceList() {
      this.loadMoreStatus = 'loading';
      getDeviceStatus().then(res => {
        // 根据查询条件过滤
        let filteredData = res.data;
        if (this.queryParams.deviceName) {
          filteredData = filteredData.filter(item => 
            item.deviceName && item.deviceName.includes(this.queryParams.deviceName)
          );
        }
        if (this.queryParams.deviceCode) {
          filteredData = filteredData.filter(item => 
            item.deviceCode && item.deviceCode.includes(this.queryParams.deviceCode)
          );
        }
        
        this.deviceList = filteredData;
        this.loadMoreStatus = this.deviceList.length > 0 ? 'noMore' : 'noMore';
        console.log('设备状态列表:', this.deviceList);
      }).catch(err => {
        console.error('获取设备状态失败:', err);
        this.$modal.showToast('获取设备状态失败');
        this.loadMoreStatus = 'more';
      });
    },
    
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getDeviceList();
    },
    
    // 重置按钮操作
    resetQuery() {
      this.queryParams = {
        deviceName: '',
        deviceCode: '',
        pageNum: 1,
        pageSize: 10
      };
      this.getDeviceList();
    },
    
    // 刷新设备列表
    refreshDeviceList() {
      this.$modal.showToast('正在刷新设备列表...');
      this.getDeviceList();
    },
    
    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '无心跳记录';
      
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },
    
    // 获取状态文本
    getStatusText(status) {
      if (status === 'CONNECTED') {
        return '在线';
      } else if (status === 'DISCONNECTED') {
        return '离线';
      } else {
        return '未知';
      }
    },
    
    // 查看设备详情
    viewDeviceDetail(id) {
      uni.navigateTo({
        url: `/pages/device/detail?id=${id}`,
        fail: (err) => {
          console.error('跳转到设备详情页面失败:', err);
          this.$modal.showToast('页面跳转失败');
        }
      });
    },
    
    // 处理开闸操作
    handleOpenGate(id) {
      if (!checkPermi(['asc:device:manage'])) {
        this.$modal.showToast('您没有操作权限');
        return;
      }
      
      uni.showModal({
        title: '确认操作',
        content: '确定要进行开闸操作吗？',
        success: res => {
          if (res.confirm) {
            this.$modal.showLoading('正在开闸...');
            openGate(id).then(res => {
              this.$modal.hideLoading();
              this.$modal.showSuccess('开闸成功');
            }).catch(err => {
              this.$modal.hideLoading();
              this.$modal.showError('开闸失败: ' + (err.message || '未知错误'));
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
.device-container {
  padding-bottom: 20px;
}

.search-box {
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.search-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.search-input {
  width: 200px;
  height: 35px;
  padding: 0 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.cu-btn {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 0 15px;
  height: 35px;
  line-height: 35px;
  font-size: 14px;
  border-radius: 4px;
}

.bg-blue {
  background-color: #2979ff;
  color: #fff;
}

.line-blue {
  background-color: transparent;
  color: #2979ff;
  border: 1px solid #2979ff;
}

.bg-green {
  background-color: #19be6b;
  color: #fff;
}

.list-box {
  padding: 10px;
}

.empty-box {
  padding: 30px 0;
  text-align: center;
  color: #999;
}

.list-item {
  display: flex;
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
}

.item-main {
  flex: 1;
}

.item-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.device-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-right: 10px;
}

.device-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-online {
  background-color: #19BE6B;
  color: #fff;
}

.status-offline {
  background-color: #FA3534;
  color: #fff;
}

.item-info {
  display: flex;
  flex-direction: column;
  font-size: 14px;
  color: #666;
}

.item-info text {
  margin-bottom: 5px;
}

.item-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 10px;
}

.action-btn {
  margin-bottom: 10px;
  min-width: 70px;
}
</style> 
@font-face {
  font-family: "iconfont";
  src: url('./iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  display: inline-block;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-user:before {
  content: "\e7ae";
}

.icon-password:before {
  content: "\e8b2";
}

.icon-code:before {
  content: "\e699";
}

.icon-setting:before {
  content: "\e6cc";
}

.icon-share:before {
  content: "\e739";
}

.icon-edit:before {
  content: "\e60c";
}

.icon-version:before {
  content: "\e63f";
}

.icon-service:before {
  content: "\e6ff";
}

.icon-friendfill:before {
  content: "\e726";
}

.icon-community:before {
  content: "\e741";
}

.icon-people:before {
  content: "\e736";
}

.icon-dianzan:before {
  content: "\ec7f";
}

.icon-right:before {
  content: "\e7eb";
}

.icon-logout:before {
  content: "\e61d";
}

.icon-help:before {
  content: "\e616";
}

.icon-github:before {
  content: "\e628";
}

.icon-aixin:before {
  content: "\e601";
}

.icon-clean:before {
  content: "\e607";
}

.icon-refresh:before {
  content: "\e604";
}



export const fontData = [
  {
    "font_class": "arrow-down",
    "unicode": "\ue6be"
  },
  {
    "font_class": "arrow-left",
    "unicode": "\ue6bc"
  },
  {
    "font_class": "arrow-right",
    "unicode": "\ue6bb"
  },
  {
    "font_class": "arrow-up",
    "unicode": "\ue6bd"
  },
  {
    "font_class": "auth",
    "unicode": "\ue6ab"
  },
  {
    "font_class": "auth-filled",
    "unicode": "\ue6cc"
  },
  {
    "font_class": "back",
    "unicode": "\ue6b9"
  },
  {
    "font_class": "bars",
    "unicode": "\ue627"
  },
  {
    "font_class": "calendar",
    "unicode": "\ue6a0"
  },
  {
    "font_class": "calendar-filled",
    "unicode": "\ue6c0"
  },
  {
    "font_class": "camera",
    "unicode": "\ue65a"
  },
  {
    "font_class": "camera-filled",
    "unicode": "\ue658"
  },
  {
    "font_class": "cart",
    "unicode": "\ue631"
  },
  {
    "font_class": "cart-filled",
    "unicode": "\ue6d0"
  },
  {
    "font_class": "chat",
    "unicode": "\ue65d"
  },
  {
    "font_class": "chat-filled",
    "unicode": "\ue659"
  },
  {
    "font_class": "chatboxes",
    "unicode": "\ue696"
  },
  {
    "font_class": "chatboxes-filled",
    "unicode": "\ue692"
  },
  {
    "font_class": "chatbubble",
    "unicode": "\ue697"
  },
  {
    "font_class": "chatbubble-filled",
    "unicode": "\ue694"
  },
  {
    "font_class": "checkbox",
    "unicode": "\ue62b"
  },
  {
    "font_class": "checkbox-filled",
    "unicode": "\ue62c"
  },
  {
    "font_class": "checkmarkempty",
    "unicode": "\ue65c"
  },
  {
    "font_class": "circle",
    "unicode": "\ue65b"
  },
  {
    "font_class": "circle-filled",
    "unicode": "\ue65e"
  },
  {
    "font_class": "clear",
    "unicode": "\ue66d"
  },
  {
    "font_class": "close",
    "unicode": "\ue673"
  },
  {
    "font_class": "closeempty",
    "unicode": "\ue66c"
  },
  {
    "font_class": "cloud-download",
    "unicode": "\ue647"
  },
  {
    "font_class": "cloud-download-filled",
    "unicode": "\ue646"
  },
  {
    "font_class": "cloud-upload",
    "unicode": "\ue645"
  },
  {
    "font_class": "cloud-upload-filled",
    "unicode": "\ue648"
  },
  {
    "font_class": "color",
    "unicode": "\ue6cf"
  },
  {
    "font_class": "color-filled",
    "unicode": "\ue6c9"
  },
  {
    "font_class": "compose",
    "unicode": "\ue67f"
  },
  {
    "font_class": "contact",
    "unicode": "\ue693"
  },
  {
    "font_class": "contact-filled",
    "unicode": "\ue695"
  },
  {
    "font_class": "down",
    "unicode": "\ue6b8"
  },
	{
	  "font_class": "bottom",
	  "unicode": "\ue6b8"
	},
  {
    "font_class": "download",
    "unicode": "\ue68d"
  },
  {
    "font_class": "download-filled",
    "unicode": "\ue681"
  },
  {
    "font_class": "email",
    "unicode": "\ue69e"
  },
  {
    "font_class": "email-filled",
    "unicode": "\ue69a"
  },
  {
    "font_class": "eye",
    "unicode": "\ue651"
  },
  {
    "font_class": "eye-filled",
    "unicode": "\ue66a"
  },
  {
    "font_class": "eye-slash",
    "unicode": "\ue6b3"
  },
  {
    "font_class": "eye-slash-filled",
    "unicode": "\ue6b4"
  },
  {
    "font_class": "fire",
    "unicode": "\ue6a1"
  },
  {
    "font_class": "fire-filled",
    "unicode": "\ue6c5"
  },
  {
    "font_class": "flag",
    "unicode": "\ue65f"
  },
  {
    "font_class": "flag-filled",
    "unicode": "\ue660"
  },
  {
    "font_class": "folder-add",
    "unicode": "\ue6a9"
  },
  {
    "font_class": "folder-add-filled",
    "unicode": "\ue6c8"
  },
  {
    "font_class": "font",
    "unicode": "\ue6a3"
  },
  {
    "font_class": "forward",
    "unicode": "\ue6ba"
  },
  {
    "font_class": "gear",
    "unicode": "\ue664"
  },
  {
    "font_class": "gear-filled",
    "unicode": "\ue661"
  },
  {
    "font_class": "gift",
    "unicode": "\ue6a4"
  },
  {
    "font_class": "gift-filled",
    "unicode": "\ue6c4"
  },
  {
    "font_class": "hand-down",
    "unicode": "\ue63d"
  },
  {
    "font_class": "hand-down-filled",
    "unicode": "\ue63c"
  },
  {
    "font_class": "hand-up",
    "unicode": "\ue63f"
  },
  {
    "font_class": "hand-up-filled",
    "unicode": "\ue63e"
  },
  {
    "font_class": "headphones",
    "unicode": "\ue630"
  },
  {
    "font_class": "heart",
    "unicode": "\ue639"
  },
  {
    "font_class": "heart-filled",
    "unicode": "\ue641"
  },
  {
    "font_class": "help",
    "unicode": "\ue679"
  },
  {
    "font_class": "help-filled",
    "unicode": "\ue674"
  },
  {
    "font_class": "home",
    "unicode": "\ue662"
  },
  {
    "font_class": "home-filled",
    "unicode": "\ue663"
  },
  {
    "font_class": "image",
    "unicode": "\ue670"
  },
  {
    "font_class": "image-filled",
    "unicode": "\ue678"
  },
  {
    "font_class": "images",
    "unicode": "\ue650"
  },
  {
    "font_class": "images-filled",
    "unicode": "\ue64b"
  },
  {
    "font_class": "info",
    "unicode": "\ue669"
  },
  {
    "font_class": "info-filled",
    "unicode": "\ue649"
  },
  {
    "font_class": "left",
    "unicode": "\ue6b7"
  },
  {
    "font_class": "link",
    "unicode": "\ue6a5"
  },
  {
    "font_class": "list",
    "unicode": "\ue644"
  },
  {
    "font_class": "location",
    "unicode": "\ue6ae"
  },
  {
    "font_class": "location-filled",
    "unicode": "\ue6af"
  },
  {
    "font_class": "locked",
    "unicode": "\ue66b"
  },
  {
    "font_class": "locked-filled",
    "unicode": "\ue668"
  },
  {
    "font_class": "loop",
    "unicode": "\ue633"
  },
  {
    "font_class": "mail-open",
    "unicode": "\ue643"
  },
  {
    "font_class": "mail-open-filled",
    "unicode": "\ue63a"
  },
  {
    "font_class": "map",
    "unicode": "\ue667"
  },
  {
    "font_class": "map-filled",
    "unicode": "\ue666"
  },
  {
    "font_class": "map-pin",
    "unicode": "\ue6ad"
  },
  {
    "font_class": "map-pin-ellipse",
    "unicode": "\ue6ac"
  },
  {
    "font_class": "medal",
    "unicode": "\ue6a2"
  },
  {
    "font_class": "medal-filled",
    "unicode": "\ue6c3"
  },
  {
    "font_class": "mic",
    "unicode": "\ue671"
  },
  {
    "font_class": "mic-filled",
    "unicode": "\ue677"
  },
  {
    "font_class": "micoff",
    "unicode": "\ue67e"
  },
  {
    "font_class": "micoff-filled",
    "unicode": "\ue6b0"
  },
  {
    "font_class": "minus",
    "unicode": "\ue66f"
  },
  {
    "font_class": "minus-filled",
    "unicode": "\ue67d"
  },
  {
    "font_class": "more",
    "unicode": "\ue64d"
  },
  {
    "font_class": "more-filled",
    "unicode": "\ue64e"
  },
  {
    "font_class": "navigate",
    "unicode": "\ue66e"
  },
  {
    "font_class": "navigate-filled",
    "unicode": "\ue67a"
  },
  {
    "font_class": "notification",
    "unicode": "\ue6a6"
  },
  {
    "font_class": "notification-filled",
    "unicode": "\ue6c1"
  },
  {
    "font_class": "paperclip",
    "unicode": "\ue652"
  },
  {
    "font_class": "paperplane",
    "unicode": "\ue672"
  },
  {
    "font_class": "paperplane-filled",
    "unicode": "\ue675"
  },
  {
    "font_class": "person",
    "unicode": "\ue699"
  },
  {
    "font_class": "person-filled",
    "unicode": "\ue69d"
  },
  {
    "font_class": "personadd",
    "unicode": "\ue69f"
  },
  {
    "font_class": "personadd-filled",
    "unicode": "\ue698"
  },
  {
    "font_class": "personadd-filled-copy",
    "unicode": "\ue6d1"
  },
  {
    "font_class": "phone",
    "unicode": "\ue69c"
  },
  {
    "font_class": "phone-filled",
    "unicode": "\ue69b"
  },
  {
    "font_class": "plus",
    "unicode": "\ue676"
  },
  {
    "font_class": "plus-filled",
    "unicode": "\ue6c7"
  },
  {
    "font_class": "plusempty",
    "unicode": "\ue67b"
  },
  {
    "font_class": "pulldown",
    "unicode": "\ue632"
  },
  {
    "font_class": "pyq",
    "unicode": "\ue682"
  },
  {
    "font_class": "qq",
    "unicode": "\ue680"
  },
  {
    "font_class": "redo",
    "unicode": "\ue64a"
  },
  {
    "font_class": "redo-filled",
    "unicode": "\ue655"
  },
  {
    "font_class": "refresh",
    "unicode": "\ue657"
  },
  {
    "font_class": "refresh-filled",
    "unicode": "\ue656"
  },
  {
    "font_class": "refreshempty",
    "unicode": "\ue6bf"
  },
  {
    "font_class": "reload",
    "unicode": "\ue6b2"
  },
  {
    "font_class": "right",
    "unicode": "\ue6b5"
  },
  {
    "font_class": "scan",
    "unicode": "\ue62a"
  },
  {
    "font_class": "search",
    "unicode": "\ue654"
  },
  {
    "font_class": "settings",
    "unicode": "\ue653"
  },
  {
    "font_class": "settings-filled",
    "unicode": "\ue6ce"
  },
  {
    "font_class": "shop",
    "unicode": "\ue62f"
  },
  {
    "font_class": "shop-filled",
    "unicode": "\ue6cd"
  },
  {
    "font_class": "smallcircle",
    "unicode": "\ue67c"
  },
  {
    "font_class": "smallcircle-filled",
    "unicode": "\ue665"
  },
  {
    "font_class": "sound",
    "unicode": "\ue684"
  },
  {
    "font_class": "sound-filled",
    "unicode": "\ue686"
  },
  {
    "font_class": "spinner-cycle",
    "unicode": "\ue68a"
  },
  {
    "font_class": "staff",
    "unicode": "\ue6a7"
  },
  {
    "font_class": "staff-filled",
    "unicode": "\ue6cb"
  },
  {
    "font_class": "star",
    "unicode": "\ue688"
  },
  {
    "font_class": "star-filled",
    "unicode": "\ue68f"
  },
  {
    "font_class": "starhalf",
    "unicode": "\ue683"
  },
  {
    "font_class": "trash",
    "unicode": "\ue687"
  },
  {
    "font_class": "trash-filled",
    "unicode": "\ue685"
  },
  {
    "font_class": "tune",
    "unicode": "\ue6aa"
  },
  {
    "font_class": "tune-filled",
    "unicode": "\ue6ca"
  },
  {
    "font_class": "undo",
    "unicode": "\ue64f"
  },
  {
    "font_class": "undo-filled",
    "unicode": "\ue64c"
  },
  {
    "font_class": "up",
    "unicode": "\ue6b6"
  },
	{
	  "font_class": "top",
	  "unicode": "\ue6b6"
	},
  {
    "font_class": "upload",
    "unicode": "\ue690"
  },
  {
    "font_class": "upload-filled",
    "unicode": "\ue68e"
  },
  {
    "font_class": "videocam",
    "unicode": "\ue68c"
  },
  {
    "font_class": "videocam-filled",
    "unicode": "\ue689"
  },
  {
    "font_class": "vip",
    "unicode": "\ue6a8"
  },
  {
    "font_class": "vip-filled",
    "unicode": "\ue6c6"
  },
  {
    "font_class": "wallet",
    "unicode": "\ue6b1"
  },
  {
    "font_class": "wallet-filled",
    "unicode": "\ue6c2"
  },
  {
    "font_class": "weibo",
    "unicode": "\ue68b"
  },
  {
    "font_class": "weixin",
    "unicode": "\ue691"
  }
]

// export const fontData = JSON.parse<IconsDataItem>(fontDataJson)

/* ==================
   图标样式 - 使用 Unicode 字符替代字体图标
 ==================== */

/* 基础图标类 */
.icon {
  display: inline-block;
  font-style: normal;
  font-size: 16px;
  line-height: 1;
}

/* 使用 Unicode 字符的图标 */
.icon-user::before { content: "👤"; }
.icon-password::before { content: "🔒"; }
.icon-code::before { content: "🔢"; }
.icon-setting::before { content: "⚙️"; }
.icon-share::before { content: "📤"; }
.icon-edit::before { content: "✏️"; }
.icon-version::before { content: "📋"; }
.icon-service::before { content: "🛠️"; }
.icon-friendfill::before { content: "👥"; }
.icon-community::before { content: "🏘️"; }
.icon-people::before { content: "👨‍👩‍👧‍👦"; }
.icon-dianzan::before { content: "👍"; }
.icon-right::before { content: "▶️"; }
.icon-logout::before { content: "🚪"; }
.icon-help::before { content: "❓"; }
.icon-github::before { content: "🐙"; }
.icon-aixin::before { content: "❤️"; }
.icon-clean::before { content: "🧹"; }
.icon-refresh::before { content: "🔄"; }

/* 或者使用简单的文字符号 */
.icon-arrow-right::before { content: "→"; }
.icon-arrow-left::before { content: "←"; }
.icon-arrow-up::before { content: "↑"; }
.icon-arrow-down::before { content: "↓"; }
.icon-close::before { content: "×"; }
.icon-check::before { content: "✓"; }
.icon-plus::before { content: "+"; }
.icon-minus::before { content: "-"; }
.icon-star::before { content: "★"; }
.icon-heart::before { content: "♥"; }

/* 图标大小变体 */
.icon-sm { font-size: 12px; }
.icon-md { font-size: 16px; }
.icon-lg { font-size: 20px; }
.icon-xl { font-size: 24px; }

/* 图标颜色 */
.icon-primary { color: #007AFF; }
.icon-success { color: #4CD964; }
.icon-warning { color: #FF9500; }
.icon-danger { color: #FF3B30; }
.icon-info { color: #5AC8FA; }
.icon-secondary { color: #8E8E93; }

<template>
  <view>
    <!-- <uni-section title="基础样式" type="line" padding> -->
    <uni-section title="生产组织" type="line"></uni-section>
    <uni-grid :column="3" :highlight="true" @change="change">
      <uni-grid-item v-for="(item, index) in orgs" :index="index" :key="item.orgId || index" wx:key="orgId">
        <view class="grid-item-box">
          <img type="image" :src="item.url" mode="aspectFill" scaleType='fitCenter' color="#777" />
          <text class="text">{{item.orgName}}</text>
        </view>
      </uni-grid-item>
    </uni-grid>
    <!-- </uni-section> -->
    <uni-section title="生产管理" type="line"></uni-section>
    <!-- <uni-grid :column="3" :showBorder="false" @change="changeGrid">
      <uni-grid-item>
        <view class="grid-item-box">
          <uni-icons type="person-filled" size="30"></uni-icons>
          <text class="text">用户管理</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item-box">
          <img src="/static/images/tabbar/home.png" style="width: 30px;" mode='scaleToFill' />
          <text class="text">用户管理</text>
        </view>
      </uni-grid-item>
      <uni-grid-item>
        <view class="grid-item-box">
          <img src="/static/images/tabbar/home.png" style="width: 30px;" mode='scaleToFill' />
          <text class="text">用户管理</text>
        </view>
      </uni-grid-item>
    </uni-grid> -->
  </view>
</template>

<script>
  import {
    api_list_org
  } from "@/api/ems/ems.js"

  export default {
    data() {
      return {
        orgs: []
      }
    },
    onLoad() {
      this.listOrg();
    },
    methods: {
      change(e) {
        let {
          index
        } = e.detail;
        let org = this.orgs[index];
        console.log(e);
        // uni.showToast({
        // 	title: `点击${org.deptName}`,
        // 	icon: 'none'
        // })
        uni.navigateTo({
          url: '../ems/ems?tagOrgid=' + org.orgId
        })
      },
      listOrg() {
        api_list_org().then(response => {
          console.log(response.rows);
          this.orgs = response.rows;
        })
      },
    }
  }
</script>

<style lang="scss">
  .image {
    width: 25px;
    height: 25px;
  }

  .text {
    font-size: 14px;
    margin-top: 5px;
  }

  .example-body {
    /* #ifndef APP-NVUE */
    // display: block;
    /* #endif */
  }

  .grid-dynamic-box {
    margin-bottom: 15px;
  }

  .grid-item-box {
    flex: 1;
    // position: relative;
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
  }

  .grid-item-box-row {
    flex: 1;
    // position: relative;
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
  }

  .grid-dot {
    position: absolute;
    top: 5px;
    right: 15px;
  }

  .swiper {
    height: 420px;
  }
</style>
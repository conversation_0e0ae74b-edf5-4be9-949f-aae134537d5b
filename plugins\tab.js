export default {
	// navigateTo, redirectTo 只能打开非 tabBar 页面。
	// switchTab 只能打开 tabBar 页面。
	// reLaunch 可以打开任意页面。
	// 页面底部的 tabBar 由页面决定，即只要是定义为 tabBar 的页面，底部都有 tabBar。
	// 调用页面路由带的参数可以在目标页面的onLoad中获取。
  // 关闭所有页面，打开到应用内的某个页面
  reLaunch(url) {
    return uni.reLaunch({
      url: url
    })
  },
  // 跳转到tabBar页面，并关闭其他所有非tabBar页面
  switchTab(url) {
    return uni.switchTab({
      url: url
    })
  },
  // 关闭当前页面，跳转到应用内的某个页面
  redirectTo(url) {
    return uni.redirectTo({
      url: url
    })
  },
  // 保留当前页面，跳转到应用内的某个页面
  navigateTo(url) {
    return uni.navigateTo({
      url: url
    })
  },
  // 关闭当前页面，返回上一页面或多级页面
  navigateBack() {
    return uni.navigateBack()
  }
}

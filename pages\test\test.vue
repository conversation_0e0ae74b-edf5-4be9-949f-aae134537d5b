<!-- TreeList.vue -->
<template>
  <view>
    <tree-item v-for="item in treeData" :key="item.id" :item="item"></tree-item>
  </view>
</template>

<script>
  import TreeItem from '@/components/TreeItem.vue';

  export default {
    name: 'TreeList',
    components: {
      TreeItem
    },
    data() {
      return {
        treeData: [{
            id: 1,
            name: 'Node 1',
            children: [{
                id: 2,
                name: 'Node 1.1',
                children: [{
                  id: 3,
                  name: 'Node 1.1.1',
                  children: []
                }]
              },
              {
                id: 4,
                name: 'Node 1.2',
                children: []
              }
            ]
          },
          {
            id: 5,
            name: 'Node 2',
            children: []
          }
        ]
      };
    }
  }
</script>
<template>
  <view class="container">
    <!-- 消息和新闻区域 -->
    <view class="info-section">
      <!-- 企业动态 -->
      <view class="section">
        <view class="section-title">企业动态</view>
        <view class="empty-placeholder">
          <text class="icon">📢</text>
          <text class="empty-text">暂无企业动态</text>
        </view>
      </view>

      <!-- 通知公告 -->
      <view class="section">
        <view class="section-title">通知公告</view>
        <view class="empty-placeholder">
          <text class="icon">📣</text>
          <text class="empty-text">暂无通知公告</text>
        </view>
      </view>
   </view>
  </view>
</template>

<script>
import { checkPermi } from '@/utils/permission';
import { getToken } from '@/utils/auth'

export default {
  onShow() {
    this.checkLogin()
  },
  methods: {
    checkPermi,
    checkLogin() {
      if (!getToken()) {
        uni.reLaunch({
          url: '/pages/auth/login'
        })
      }
    }
  },
  computed: {

  }
}
</script>


<style lang="scss">

.container {
  padding: 32rpx;
}

.company-info {
  display: flex;
  align-items: center;
}

.info-section {
  margin-bottom: 20px;
}

.section {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 2px solid #007AFF;
}

.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  background-color: #f8f8f8;
  border-radius: 4px;

  .icon {
    font-size: 30px;
    margin-bottom: 10px;
  }

  .empty-text {
    font-size: 14px;
    color: #999;
  }
}

.grid-item-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.text {
  font-size: 14px;
  margin-top: 5px;
}
</style>

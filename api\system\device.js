import request from '@/utils/request'

/** 查询车牌机设备列表 */
export function listDevice(query) {
  return request({
    url: '/asc/device/list',
    method: 'get',
    params: query
  })
}

/** 获取车牌机设备详细信息 */
export function getDevice(id) {
  return request({
    url: `/asc/device/${id}`,
    method: 'get'
  })
}

/** 新增车牌机设备 */
export function addDevice(data) {
  return request({
    url: '/asc/device',
    method: 'post',
    data: data
  })
}

/** 修改车牌机设备 */
export function updateDevice(data) {
  return request({
    url: '/asc/device',
    method: 'put',
    data: data
  })
}

/** 删除车牌机设备 */
export function delDevice(ids) {
  return request({
    url: `/asc/device/${ids}`,
    method: 'delete'
  })
}

/** 获取设备连接状态 */
export function getDeviceStatus() {
  return request({
    url: '/asc/device/status',
    method: 'get'
  })
}

/** 手动控制开闸 */
export function openGate(id) {
  return request({
    url: `/asc/device/openGate/${id}`,
    method: 'post'
  })
} 
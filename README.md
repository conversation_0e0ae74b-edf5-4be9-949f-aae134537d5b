<p align="center">
  <img src="https://oscimg.oschina.net/oscnet/up-43e3941654fa3054c9684bf53d1b1d356a1.png" alt="logo">
</p>

<h1 align="center">山西高义办公系统 UniApp 移动端 (v1.1.0)</h1>

[![Version](https://img.shields.io/badge/version-v1.1.0-blue)]() [![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)

## 目录

- [项目简介](#项目简介)
- [主要功能](#主要功能)
- [技术栈](#技术栈)
- [项目结构](#项目结构)
- [核心模块说明](#核心模块说明)
- [环境搭建](#环境搭建)
- [开发 & 运行](#开发--运行)
- [打包 & 发布](#打包--发布)
- [编码规范](#编码规范)
- [贡献指南](#贡献指南)
- [许可证](#许可证)
- [联系方式](#联系方式)

## 项目简介

山西高义办公系统移动端项目，基于 UniApp (Vue2) 开发，实现一次开发，多端（小程序、H5、App）部署。提供移动办公全流程支持，包括审批、报修、资产管理等核心业务。

## 主要功能

1. **用户认证**：登录/登出、Token 管理、角色权限
2. **权限控制**：基于角色/权限的页面和接口访问管控
3. **工作台**：任务列表、审批流管理
4. **EMS模块**：设备资产登记、报修申请、统计报表
5. **个人中心**：用户信息、设置、帮助文档
6. **通用组件**：表单、列表、图表等 UI 组件

## 技术栈

- 开发框架：UniApp (Vue2)
- UI 组件：uni-ui
- 状态管理：Vuex
- 语言：JavaScript (ES6+)
- 样式：SCSS
- 构建工具：HBuilderX / Vue CLI

## 项目结构

```text
├── api/               # 后端接口定义
│   ├── login.js       # 登录认证相关
│   ├── ems/           # EMS 相关接口
│   └── system/        # 系统管理接口
├── store/             # Vuex 状态管理
│   ├── modules/       # 模块化管理
│   ├── getters.js     # 全局 getters
│   └── index.js       # Store 入口
├── utils/             # 工具函数
│   ├── request.js     # 网络请求封装
│   ├── auth.js        # Token 存取
│   └── permission.js  # 前端权限控制
├── pages/             # 页面组件
│   ├── login.vue      # 登录页
│   ├── index.vue      # 首页
│   ├── work/          # 工作台模块
│   ├── ems/           # EMS 模块
│   ├── mine/          # 个人中心
│   └── common/        # 通用页面
├── components/        # 公共组件
├── plugins/           # 插件和扩展
├── static/            # 静态资源
├── uni_modules/       # UniApp 官方模块
├── config.js          # 全局配置
├── manifest.json      # 应用配置 (版本、appid 等)
├── pages.json         # 页面路由 & TabBar 配置
├── main.js            # 应用入口
└── uni.scss           # 全局样式变量
```

## 核心模块说明

### api
- `login.js`：认证、获取用户信息
- `ems/`：设备管理、报修、统计
- `system/`：菜单、字典等系统数据

### store
- `modules/user.js`：用户信息、权限状态
- `getters.js`：全局状态访问方式

### utils
- `request.js`：axios 封装，支持自动注入 Token、统一错误处理
- `auth.js`：Token 的获取、存储、移除
- `permission.js`：前端路由/页面权限校验

### pages.json
- 页面路由注册
- 全局样式、导航栏配置
- TabBar 配置 (首页、工作台、我的)

## 环境搭建

1. 安装 HBuilderX 或 Vue CLI
2. 安装依赖

```bash
npm install
```

## 开发 & 运行

```bash
# 小程序 (微信)
npm run dev:mp-weixin

# H5
npm run dev:h5

# App (Android/iOS)
npm run dev:app-plus
```

## 打包 & 发布

```bash
# 小程序
npm run build:mp-weixin
# H5
npm run build:h5
# App
npm run build:app-plus
```

## 编码规范

- SCSS 预处理，变量定义在 `static/scss/variables.scss`
- 新页面需在 `pages.json` 注册并与目录路径保持一致
- 公共组件放 `components/`，优先使用 `uni_modules/`
- API 调用用 `utils/request.js` 封装方法

## 贡献指南

1. Fork 本仓库 → 新建分支 → 提交 PR
2. 提 Issue 请描述复现步骤和期望行为

## 许可证

本项目遵循 [MIT License](LICENSE)

## 联系方式

维护人：若依
邮箱：<EMAIL>

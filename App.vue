<script>
  import config from './config'
  import store from '@/store'
  import { getToken } from '@/utils/auth'

  export default {
    onLaunch: function() {
      this.initApp()
    },
    methods: {
      // 初始化应用
      initApp() {
        // 初始化应用配置
        this.initConfig()
        // 检查用户登录状态
        this.checkLogin()
      },
      initConfig() {
        this.globalData.config = config
      },
      checkLogin() {
        if (!getToken()) {
          uni.reLaunch({
            url: '/pages/auth/login'
          })
        }
      }
    }
  }
</script>

<style lang="scss">
  @import '@/static/scss/main.scss';
</style>

<!-- 暂时注释掉字体文件，避免路径问题
<style>
  @import '@/static/font/iconfont.css';
</style>
-->
>

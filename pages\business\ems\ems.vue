<template>
	
	<view>
		<uni-list>
			<view v-for="tag in tags" :key="tag.tagId" :title="tag.tagName">
				<view @click="showChart(tag,$event)"
					style="
						margin: 1%;
						padding: 5%;
						border: 3rpx solid black;
						border-radius: 15rpx;
						border-color: black;">
					<!-- <text>标签ID：{{tag.tagId}}</text> -->
					<text>标签名称：{{tag.tagName}}</text></br>
					<!-- <text>标签地址：{{tag.tagAddr}}</text> -->
					<!-- <text>更新间隔：{{tag.tagQueryInterval}} 秒</text> -->
					<text>标签数值：{{tag.tagCurrentVal}}</text></br>
					<text>更新时间：{{tag.tagCurrentTime}}</text></br>
				</view>
			</view>
		</uni-list>
	</view>
</template>

<script>
	import { api_query_tags } from "@/api/ems/ems.js"
	export default {
		data() {
			return {
				tags : [],
				tagOrgid : "",
				clickedId :"",
			}
		},
		
		onLoad(e) {
			if(e.tagOrgid){
				this.tagOrgid = e.tagOrgid;
			}
			this.queryTags();
		},
		
		onPullDownRefresh() {
			this.queryTags();
			uni.stopPullDownRefresh()  //刷新数据之后停止刷新效果
		},
		
		methods: {
showChart(tag,e){
uni.navigateTo({
url: '/pages/business/ems/charts?tagId=' + tag.tagId + '&title=' + tag.tagName
})
},
			
			queryTags(){
				api_query_tags({'businessType':'ems','tagOrgid':this.tagOrgid}).then(response => {
					console.log(response.rows)
					this.tags = response.rows})
			}
		},
	}
</script>

<style>

</style>
